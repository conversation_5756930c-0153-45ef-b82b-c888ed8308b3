import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
  Image,
} from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import {
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { MagicLinkService } from '@/services/magicLinkService';
import { useDeepLinking } from '@/lib/hooks/useDeepLinking';
import { DesignSystem } from '@/constants/DesignSystem';
import { validateEmail } from '@/lib/utils/validation';
import { handleAuthError, withRetry } from '@/lib/utils/errorHandling';
import { AnimatedView, ScaleTransition, ShakeView } from '@/components/animated/AnimatedComponents';
import { getAccessibilityProps, announceForAccessibility } from '@/lib/utils/accessibility';
import { getResponsiveLayout } from '@/lib/utils/responsive';

const { height } = Dimensions.get('window');

export default function LoginScreen() {
  const { email: paramEmail } = useLocalSearchParams<{ email?: string }>();
  const [email, setEmail] = useState(paramEmail || '');
  const [emailError, setEmailError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Responsive layout
  const responsiveLayout = getResponsiveLayout();

  // Animation values for success message
  const successScale = useSharedValue(0);



  // Initialize deep linking
  useDeepLinking();

  useEffect(() => {
    // Clean up expired magic link tokens on app start
    MagicLinkService.cleanupExpiredTokens();
  }, []);

  const validateEmailField = (emailValue: string): boolean => {
    const result = validateEmail(emailValue);
    if (!result.isValid) {
      setEmailError(result.error || 'Invalid email');
      return false;
    }
    setEmailError('');
    return true;
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (emailError) {
      // Clear error when user starts typing
      setEmailError('');
    }
  };

  const handleEmailLogin = async () => {
    if (!validateEmailField(email)) {
      return;
    }

    setIsLoading(true);
    try {
      console.log('Sending magic link to:', email);

      // Use retry mechanism for better reliability
      const result = await withRetry(
        () => MagicLinkService.sendMagicLink({
          email: email.trim(),
          redirectUrl: '/(tabs)',
        }),
        3, // max retries
        1000 // delay between retries
      );

      if (result.success) {
        setShowSuccess(true);
        successScale.value = withSpring(1, DesignSystem.Animations.spring.bouncy);

        // Announce success to screen readers
        announceForAccessibility('Magic link sent successfully. Please check your email.');

        // Auto-hide success message after 3 seconds
        setTimeout(() => {
          setShowSuccess(false);
          successScale.value = withTiming(0, { duration: 300 });
          setEmail('');
        }, 3000);
      } else {
        const appError = handleAuthError(result);
        announceForAccessibility(`Error: ${appError.userMessage}`);
        Alert.alert('Error', appError.userMessage, [{ text: 'OK' }]);
      }
    } catch (error) {
      console.error('Magic link error:', error);
      const appError = handleAuthError(error);
      Alert.alert('Error', appError.userMessage, [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = () => {
    Alert.alert('Coming Soon', 'Google login will be available soon!');
  };

  const handleGitHubLogin = () => {
    Alert.alert('Coming Soon', 'GitHub login will be available soon!');
  };



  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner 
          variant="dots" 
          size="large" 
          text="Sending magic link..."
        />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[
          DesignSystem.Colors.light.background,
          DesignSystem.Colors.light.backgroundSecondary,
        ]}
        style={StyleSheet.absoluteFill}
      />
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.content}>
          {/* Header */}
          <AnimatedView animation="slideUp" delay={200} style={styles.header}>
            <AnimatedView animation="scaleIn" delay={400} style={styles.logoContainer}>
              <Image
                source={require('@/assets/images/icon.png')}
                style={styles.logo}
                resizeMode="contain"
              />
            </AnimatedView>

            <AnimatedView animation="fadeIn" delay={600}>
              <ThemedText
                type="title"
                style={styles.title}
                {...getAccessibilityProps.heading('Welcome Back', 1)}
              >
                Welcome Back
              </ThemedText>
            </AnimatedView>

            <AnimatedView animation="fadeIn" delay={800}>
              <ThemedText
                style={styles.subtitle}
                {...getAccessibilityProps.heading('Sign in to your PROTEC Alumni account', 2)}
              >
                Sign in to your PROTEC Alumni account
              </ThemedText>
            </AnimatedView>
          </AnimatedView>

          {/* Success Message */}
          <ScaleTransition visible={showSuccess}>
            <View
              style={styles.successContainer}
              {...getAccessibilityProps.alert('Magic link sent! Check your email.', 'success')}
            >
              <ThemedText style={styles.successText}>
                ✅ Magic link sent! Check your email.
              </ThemedText>
            </View>
          </ScaleTransition>

          {/* Form */}
          <AnimatedView animation="fadeIn" delay={1000} style={styles.form}>
            <ShakeView trigger={!!emailError} intensity={5}>
              <Input
                label="Email Address"
                value={email}
                onChangeText={handleEmailChange}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                error={emailError}
                variant="outlined"
                size="lg"
              />
            </ShakeView>

            <Button
              title="Send Magic Link"
              onPress={handleEmailLogin}
              disabled={isLoading || !email.trim()}
              loading={isLoading}
              variant="primary"
              size="lg"
              fullWidth
              style={styles.primaryButton}
              accessibilityLabel="Send magic link to email"
              accessibilityHint="Sends a secure login link to your email address"
            />

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <ThemedText style={styles.dividerText}>or continue with</ThemedText>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.socialButtons}>
              <Button
                title="Google"
                onPress={handleGoogleLogin}
                variant="outline"
                size="lg"
                style={styles.socialButton}
                accessibilityLabel="Sign in with Google"
                accessibilityHint="Opens Google sign in (coming soon)"
              />

              <Button
                title="GitHub"
                onPress={handleGitHubLogin}
                variant="outline"
                size="lg"
                style={styles.socialButton}
                accessibilityLabel="Sign in with GitHub"
                accessibilityHint="Opens GitHub sign in (coming soon)"
              />
            </View>
          </AnimatedView>

          {/* Footer */}
          <View style={styles.footer}>
            <ThemedText style={styles.footerText}>
              Don&apos;t have an account? Contact your alumni coordinator
            </ThemedText>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: DesignSystem.Colors.light.background,
  },

  scrollContent: {
    flexGrow: 1,
    minHeight: height,
  },

  content: {
    flex: 1,
    paddingHorizontal: DesignSystem.Spacing['3xl'],
    paddingTop: DesignSystem.Spacing['6xl'],
    paddingBottom: DesignSystem.Spacing['4xl'],
    maxWidth: 400,
    alignSelf: 'center',
    width: '100%',
  },

  header: {
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['5xl'],
  },

  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: DesignSystem.Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: DesignSystem.Spacing['3xl'],
    ...DesignSystem.Shadows.lg,
  },

  logo: {
    width: 60,
    height: 60,
  },

  title: {
    fontSize: 32,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
    color: DesignSystem.Colors.light.text,
  },

  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: DesignSystem.Colors.light.textSecondary,
    lineHeight: 24,
  },

  successContainer: {
    backgroundColor: DesignSystem.Colors.light.success,
    paddingHorizontal: DesignSystem.Spacing.lg,
    paddingVertical: DesignSystem.Spacing.md,
    borderRadius: DesignSystem.BorderRadius.lg,
    marginBottom: DesignSystem.Spacing.lg,
    alignItems: 'center',
  },

  successText: {
    color: DesignSystem.Colors.light.textInverse,
    fontWeight: DesignSystem.Typography.fontWeight.medium,
    textAlign: 'center',
  },

  form: {
    width: '100%',
    marginBottom: DesignSystem.Spacing['4xl'],
  },

  primaryButton: {
    marginTop: DesignSystem.Spacing.lg,
    marginBottom: DesignSystem.Spacing['3xl'],
  },

  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['3xl'],
  },

  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: DesignSystem.Colors.light.border,
  },

  dividerText: {
    marginHorizontal: DesignSystem.Spacing.lg,
    fontSize: DesignSystem.Typography.fontSize.sm,
    color: DesignSystem.Colors.light.textMuted,
  },

  socialButtons: {
    flexDirection: 'row',
    gap: DesignSystem.Spacing.md,
  },

  socialButton: {
    flex: 1,
  },

  footer: {
    alignItems: 'center',
    marginTop: 'auto',
  },

  footerText: {
    fontSize: DesignSystem.Typography.fontSize.sm,
    color: DesignSystem.Colors.light.textMuted,
    textAlign: 'center',
    lineHeight: 20,
  },
});
